#include "stm32f10x.h"                  // Device header
#include "Delay.h"
#include "Serial.h"
#include "ADC.h"
#include "Serial.h"
#include <string.h>
#include <stdio.h>
void UpdateHMIDisplay(void);

#define USART1_RX_BUFFER_SIZE 64
char usart1_rx_line_buffer[USART1_RX_BUFFER_SIZE];
uint8_t usart1_rx_line_idx = 0;
volatile uint8_t usart1_line_received_flag = 0;
float temp_D_float_val=0.0f;

uint8_t temp_X;
uint8_t parsed_D_value;
float parsed_D_value_float;
int parsed_X_value = 0;

// 简化：直接存储和显示测量数据
float current_distance = 0.0f;
float current_shape_size = 0.0f;
char current_shape_type[20] = "NONE";

// 新增：x变量用于存储#数据
float x = 0.0f;

// 数据缓冲区用于计算平均值
#define BUFFER_SIZE 20
float distance_buffer[BUFFER_SIZE];
float shape_buffer[BUFFER_SIZE];
float x_buffer[BUFFER_SIZE];
uint8_t distance_count = 0;
uint8_t shape_count = 0;
uint8_t x_count = 0;
uint8_t buffer_index = 0;

// 按键控制标志
uint8_t update_display_flag = 0;  // 0=不更新显示，1=更新显示

// 计算平均值函数
float calculate_average(float* buffer, uint8_t count) {
    if (count == 0) return 0.0f;
    float sum = 0.0f;
    for (uint8_t i = 0; i < count; i++) {
        sum += buffer[i];
    }
    return sum / count;
}

// 按键处理函数
void handle_start_button(void) {
    char debug_msg[100];

    Serial_SendString("=== 开始按键被按下 ===\r\n");

    // 计算并显示平均值
    if (distance_count > 0) {
        float avg_distance = calculate_average(distance_buffer, distance_count);
        current_distance = avg_distance;
        sprintf(debug_msg, "距离平均值: %.2f cm (基于 %d 个样本)\r\n", avg_distance, distance_count);
        Serial_SendString(debug_msg);
    }

    if (shape_count > 0) {
        float avg_shape = calculate_average(shape_buffer, shape_count);
        current_shape_size = avg_shape;
        sprintf(debug_msg, "图形尺寸平均值: %.2f cm (基于 %d 个样本)\r\n", avg_shape, shape_count);
        Serial_SendString(debug_msg);
    }

    if (x_count > 0) {
        float avg_x = calculate_average(x_buffer, x_count);
        x = avg_x;
        sprintf(debug_msg, "X变量平均值: %.2f (基于 %d 个样本)\r\n", avg_x, x_count);
        Serial_SendString(debug_msg);
    }

    // 更新显示屏
    UpdateHMIDisplay();

    Serial_SendString("=== 显示屏已更新为平均值 ===\r\n");
}

// 简化的HMI更新函数 - 只在按键后更新显示
void UpdateHMIDisplay(void)
{
    char hmi_cmd[100];

    // 更新距离显示
    if (current_distance > 0.0f) {
        int distance_int = (int)(current_distance * 100);


        sprintf(hmi_cmd, "index.x2.val=%d\xff\xff\xff", distance_int);
        Serial2_SendString(hmi_cmd);
        Delay_ms(10);
        Serial2_SendString("ref index.x2\xff\xff\xff");
        Delay_ms(10);

        sprintf(hmi_cmd, "距离显示更新: %.2f cm\r\n", current_distance);
        Serial_SendString(hmi_cmd);
    }

    // 优先显示x变量（#数据解析结果）- 总是显示x值，不限制范围
    int x_int = (int)(x * 100);  // x值乘100后强制转为整形

    sprintf(hmi_cmd, "index.x3.val=%d\xff\xff\xff", x_int);
    Serial2_SendString(hmi_cmd);
    Delay_ms(10);
    Serial2_SendString("ref index.x3\xff\xff\xff");
    Delay_ms(10);

    sprintf(hmi_cmd, "X变量显示更新: %.2f (整数值: %d)\r\n", x, x_int);
    Serial_SendString(hmi_cmd);

    // 如果没有x数据，则显示图形尺寸
    if (x == 0.0f && current_shape_size > 0.0f) {
        int shape_int = (int)(current_shape_size * 100);

        sprintf(hmi_cmd, "index.x3.val=%d\xff\xff\xff", shape_int);
        Serial2_SendString(hmi_cmd);
        Delay_ms(10);
        Serial2_SendString("ref index.x3\xff\xff\xff");
        Delay_ms(10);

        sprintf(hmi_cmd, "%s尺寸显示更新: %.2f cm\r\n", current_shape_type, current_shape_size);
        Serial_SendString(hmi_cmd);
    }
}

uint8_t RxData;			//定义用于接收串口数据的变量
uint8_t old_x;  // 重命名以避免与新的float x变量冲突
float I;
float P;
uint8_t D;
uint16_t ADValue;			//定义AD值变量
float Voltage;
float A;
char debug_serial1_buf[100];



void HandleUsart1RxByte(uint8_t rx_byte)
{
    // 确保缓冲区不会溢出
    if (usart1_rx_line_idx < USART1_RX_BUFFER_SIZE - 1) // 留一个位置给字符串结束符 '\0'
    {
        if (rx_byte == '\n' || rx_byte == '\r') // 检测到行结束符 (换行符或回车符)
        {
            if (usart1_rx_line_idx > 0) // 确保在行结束符之前有实际数据
            {
                usart1_rx_line_buffer[usart1_rx_line_idx] = '\0'; // 字符串以 null 结尾
                usart1_line_received_flag = 1;                   // 设置标志，通知主循环有完整行数据待处理
                Serial_SendString(">>> Line complete flag set! <<<\r\n"); // 调试信息
            }
            // 不重置索引，让主循环处理完后再重置
        }
        else if (rx_byte >= 32 && rx_byte <= 126) // 只接收可打印字符
        {
            usart1_rx_line_buffer[usart1_rx_line_idx++] = rx_byte; // 将接收到的字节存入缓冲区
        }
        // 忽略其他控制字符
    }
    else // 缓冲区已满，重置以防止溢出，并丢弃当前行（可选：可以添加错误处理）
    {
        usart1_rx_line_idx = 0;
        Serial_SendString("USART1 RX Buffer Overflow!\r\n"); // 调试信息
    }
}

int main(void)
{
	/*系统初始化延时*/
	Delay_ms(100); //等待系统稳定

	/*禁用JTAG，释放PB3、PB4、PA15引脚*/
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_AFIO, ENABLE); //开启AFIO时钟
	GPIO_PinRemapConfig(GPIO_Remap_SWJ_JTAGDisable, ENABLE); //禁用JTAG，保留SWD

	/*串口初始化*/
	Serial_Init(); //串口初始化
	Delay_ms(50); //初始化间隔

	// 初始化串口2用于与串口屏通信
	Serial2_Init();
	Delay_ms(10); //初始化间隔
	// AD_Init(); //ADC初始化 - 暂时注释掉

	char String[100];
	uint32_t counter = 0; //计数器用于连续发送

	Delay_ms(100); //等待初始化完成
	Serial_SendString("STM32 Started!\r\n");
	Serial_SendString("System Clock: 8MHz HSI\r\n");
	Serial_SendString("JTAG Disabled, SWD Enabled\r\n");
	Serial_SendString("=== 按键控制数据更新系统 ===\r\n");
	Serial_SendString("工作模式:\r\n");
	Serial_SendString("  1. 摄像头数据持续接收并存入缓冲区\r\n");
	Serial_SendString("  2. 按下HMI'开始'按键时计算平均值并更新显示\r\n");
	Serial_SendString("  3. 显示保持不变，直到下次按键\r\n");
	Serial_SendString("\r\n支持的数据格式:\r\n");
	Serial_SendString("  @DISTANCE:xx.xx -> 距离数据缓冲区\r\n");
	Serial_SendString("  @TRIANGLE:xx.xx -> 图形尺寸缓冲区\r\n");
	Serial_SendString("  @CIRCLE:xx.xx   -> 图形尺寸缓冲区\r\n");
	Serial_SendString("  @SQUARE:xx.xx   -> 图形尺寸缓冲区\r\n");
	Serial_SendString("  #xx.xx -> X变量缓冲区\r\n");
	Serial_SendString("  @xx.xx (legacy) -> 距离数据缓冲区\r\n");
	Serial_SendString("\r\nHMI按键设置: printh 40 31 55; (发送@1U)\r\n");

	// 清零显示
	Serial_SendString("初始化显示屏...\r\n");
	Serial2_SendString("index.x2.val=0\xff\xff\xff");
	Delay_ms(100);
	Serial2_SendString("index.x3.val=0\xff\xff\xff");
	Delay_ms(100);

	Serial_SendString("=== 系统就绪，等待摄像头数据和HMI按键 ===\r\n");
	Serial_SendString("缓冲区大小: 20个数据点\r\n");
	Serial_SendString("当前缓冲区状态: 距离=0, 图形=0, X=0\r\n");

	while (1)
	{
		// 简单的心跳 - 每10000次循环显示一次缓冲区状态
		if(counter % 10000 == 0) {
			sprintf(String, "缓冲区状态: 距离=%d/%d, 图形=%d/%d, X=%d/%d\r\n",
			        distance_count, BUFFER_SIZE, shape_count, BUFFER_SIZE, x_count, BUFFER_SIZE);
			Serial_SendString(String);
		}

		counter++;

		// 暂时注释掉ADC相关代码
		// ADValue = AD_GetValue(); //获取AD转换的值
		// Voltage = (float)ADValue / 4095 * 3.3;
		// A = Voltage*10/0.5; //计算电流
		// sprintf(String, "ADC: %d, Voltage: %.2fV, Current: %.2fA\r\n", ADValue, Voltage, A);
		// Serial_SendString(String);

		// 检查串口2接收 - HMI按键检测
		if(Serial2_GetLineFlag() == 1) {
			char* hmi_data = Serial2_GetLineData();
			Serial2_ClearLineFlag();

			Serial_SendString("=== HMI数据接收 ===\r\n");
			Serial_SendString("接收到: ");
			Serial_SendString(hmi_data);
			Serial_SendString("\r\n");

			// 检查是否是开始按键命令（@1格式，因为printh 40 31 55对应@1U）
			if(strstr(hmi_data, "@1") != NULL) {
				Serial_SendString("检测到开始按键！\r\n");
				handle_start_button();
			}
		}

		// 检查串口接收 - 使用中断处理的结果
		if(Serial_GetLineFlag() == 1) // 检查是否接收到完整行
		{
			usart1_line_received_flag = 0; // 清除标志位，表示已处理
			usart1_rx_line_idx = 0; // 重置索引，准备接收下一行
			Serial_SendString("=== RX from Camera ===\r\n");
			Serial_SendString("Raw data: ");
			char* line_data = Serial_GetLineData(); // 获取中断处理的数据
			Serial_SendString(line_data); // 打印整个接收缓冲区内容
			Serial_SendString("\r\n");
			uint8_t data_length = strlen(line_data);
			sprintf(debug_serial1_buf, "Data length: %d\r\n", data_length);
			Serial_SendString(debug_serial1_buf);

			// 处理摄像头发送的数据
			if (data_length > 0)
			{
				// 处理以@开头的摄像头数据（如@DISTANCE:123.45）
				if (line_data[0] == '@')
				{
					// 解析带类型标识的数据
					if (strncmp(&line_data[1], "DISTANCE:", 9) == 0)
					{
						float temp_distance;
						if (sscanf(&line_data[10], "%f", &temp_distance) == 1)
						{
							// 存入缓冲区
							if (distance_count < BUFFER_SIZE) {
								distance_buffer[distance_count++] = temp_distance;
							} else {
								// 缓冲区满，移动数据并添加新数据
								for (uint8_t i = 0; i < BUFFER_SIZE - 1; i++) {
									distance_buffer[i] = distance_buffer[i + 1];
								}
								distance_buffer[BUFFER_SIZE - 1] = temp_distance;
							}

							strcpy(current_shape_type, "距离");
							sprintf(debug_serial1_buf, "距离数据存入缓冲区: %.2f cm (缓冲区: %d/%d)\r\n",
								temp_distance, distance_count, BUFFER_SIZE);
							Serial_SendString(debug_serial1_buf);

							// 不立即更新显示，等待按键
						}
					}
					else if (strncmp(&line_data[1], "TRIANGLE:", 9) == 0)
					{
						float temp_shape;
						if (sscanf(&line_data[10], "%f", &temp_shape) == 1)
						{
							// 存入缓冲区
							if (shape_count < BUFFER_SIZE) {
								shape_buffer[shape_count++] = temp_shape;
							} else {
								// 缓冲区满，移动数据并添加新数据
								for (uint8_t i = 0; i < BUFFER_SIZE - 1; i++) {
									shape_buffer[i] = shape_buffer[i + 1];
								}
								shape_buffer[BUFFER_SIZE - 1] = temp_shape;
							}

							strcpy(current_shape_type, "三角形");
							sprintf(debug_serial1_buf, "三角形数据存入缓冲区: %.2f cm (缓冲区: %d/%d)\r\n",
								temp_shape, shape_count, BUFFER_SIZE);
							Serial_SendString(debug_serial1_buf);

							// 不立即更新显示，等待按键
						}
					}
					else if (strncmp(&line_data[1], "CIRCLE:", 7) == 0)
					{
						float temp_shape;
						if (sscanf(&line_data[8], "%f", &temp_shape) == 1)
						{
							// 存入缓冲区
							if (shape_count < BUFFER_SIZE) {
								shape_buffer[shape_count++] = temp_shape;
							} else {
								// 缓冲区满，移动数据并添加新数据
								for (uint8_t i = 0; i < BUFFER_SIZE - 1; i++) {
									shape_buffer[i] = shape_buffer[i + 1];
								}
								shape_buffer[BUFFER_SIZE - 1] = temp_shape;
							}

							strcpy(current_shape_type, "圆形");
							sprintf(debug_serial1_buf, "圆形数据存入缓冲区: 直径 %.2f cm (缓冲区: %d/%d)\r\n",
								temp_shape, shape_count, BUFFER_SIZE);
							Serial_SendString(debug_serial1_buf);

							// 不立即更新显示，等待按键
						}
					}
					else if (strncmp(&line_data[1], "SQUARE:", 7) == 0)
					{
						float temp_shape;
						if (sscanf(&line_data[8], "%f", &temp_shape) == 1)
						{
							// 存入缓冲区
							if (shape_count < BUFFER_SIZE) {
								shape_buffer[shape_count++] = temp_shape;
							} else {
								// 缓冲区满，移动数据并添加新数据
								for (uint8_t i = 0; i < BUFFER_SIZE - 1; i++) {
									shape_buffer[i] = shape_buffer[i + 1];
								}
								shape_buffer[BUFFER_SIZE - 1] = temp_shape;
							}

							strcpy(current_shape_type, "正方形");
							sprintf(debug_serial1_buf, "正方形数据存入缓冲区: 边长 %.2f cm (缓冲区: %d/%d)\r\n",
								temp_shape, shape_count, BUFFER_SIZE);
							Serial_SendString(debug_serial1_buf);

							// 不立即更新显示，等待按键
						}
					}
					else if (strncmp(&line_data[1], "NO_DETECTION:", 13) == 0)
					{
						strcpy(current_shape_type, "无检测");
						Serial_SendString("摄像头无检测数据\r\n");
						// 不清零缓冲区，保持之前的数据等待按键处理
					}
					else
					{
						// 兼容原有的纯数字格式（如@123.45）
						float temp_distance = 0.0f;
						if(sscanf(&line_data[1],"%f",&temp_distance)==1)
						{
							// 存入缓冲区
							if (distance_count < BUFFER_SIZE) {
								distance_buffer[distance_count++] = temp_distance;
							} else {
								// 缓冲区满，移动数据并添加新数据
								for (uint8_t i = 0; i < BUFFER_SIZE - 1; i++) {
									distance_buffer[i] = distance_buffer[i + 1];
								}
								distance_buffer[BUFFER_SIZE - 1] = temp_distance;
							}

							strcpy(current_shape_type, "距离");
							sprintf(debug_serial1_buf, "距离数据存入缓冲区(旧格式): %.2f cm (缓冲区: %d/%d)\r\n",
								temp_distance, distance_count, BUFFER_SIZE);
							Serial_SendString(debug_serial1_buf);

							// 不立即更新显示，等待按键
						}
						else
						{
							Serial_SendString("错误: 无法解析摄像头数据\r\n");
						}
					}
				}
				// 处理以#开头的数据（如#123.45）
				else if (line_data[0] == '#')
				{
					float temp_x = 0.0f;
					if(sscanf(&line_data[1], "%f", &temp_x) == 1)
					{
						// 存入缓冲区
						if (x_count < BUFFER_SIZE) {
							x_buffer[x_count++] = temp_x;
						} else {
							// 缓冲区满，移动数据并添加新数据
							for (uint8_t i = 0; i < BUFFER_SIZE - 1; i++) {
								x_buffer[i] = x_buffer[i + 1];
							}
							x_buffer[BUFFER_SIZE - 1] = temp_x;
						}

						sprintf(debug_serial1_buf, "#数据存入缓冲区: %.2f (缓冲区: %d/%d)\r\n",
							temp_x, x_count, BUFFER_SIZE);
						Serial_SendString(debug_serial1_buf);

						// 不立即更新显示，等待按键
					}
					else
					{
						Serial_SendString("错误: 无法解析#数据\r\n");
					}
				}
			}
		}

		Delay_ms(10); // 减少延时，提高响应速度
	}
}